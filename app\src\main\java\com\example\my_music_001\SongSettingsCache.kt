package com.example.my_music_001

import android.content.Context
import android.content.SharedPreferences
import android.net.Uri
import android.util.Log
import kotlinx.coroutines.*
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicBoolean

/**
 * 歌曲设置数据类
 */
data class SongSettings(
    val customName: String = "",
    val speed: Float = 1.0f,
    val pitch: Float = 1.0f,
    val startMs: Int = 0,
    val endMs: Int = 0,
    val lastModified: Long = System.currentTimeMillis()
)

/**
 * 歌曲设置内存缓存管理器
 * 将歌曲的各种设置缓存在内存中，减少SharedPreferences的IO操作
 */
class SongSettingsCache(private val context: Context) {
    companion object {
        private const val TAG = "SongSettingsCache"
        private const val CACHE_CLEANUP_INTERVAL = 5 * 60 * 1000L // 5分钟清理一次
        private const val CACHE_EXPIRE_TIME = 30 * 60 * 1000L // 30分钟过期
        private const val MAX_CACHE_SIZE = 100 // 最大缓存数量
    }

    private val sharedPreferences: SharedPreferences = 
        context.getSharedPreferences("MusicPlayerPrefs", Context.MODE_PRIVATE)
    
    // 内存缓存
    private val cache = ConcurrentHashMap<String, SongSettings>()
    
    // 脏数据标记（需要写回SharedPreferences的数据）
    private val dirtyKeys = ConcurrentHashMap<String, Boolean>()
    
    // 协程作用域
    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    // 是否已销毁
    private val isDestroyed = AtomicBoolean(false)
    
    // 定期清理任务
    private var cleanupJob: Job? = null

    init {
        startCleanupTask()
    }

    /**
     * 获取歌曲设置
     */
    fun getSongSettings(uri: Uri): SongSettings {
        if (isDestroyed.get()) return SongSettings()
        
        val uriString = uri.toString()
        
        // 先从缓存中获取
        cache[uriString]?.let { cached ->
            // 检查是否过期
            if (System.currentTimeMillis() - cached.lastModified < CACHE_EXPIRE_TIME) {
                Log.d(TAG, "从缓存获取设置: $uriString")
                return cached
            } else {
                // 过期了，从缓存中移除
                cache.remove(uriString)
            }
        }
        
        // 从SharedPreferences加载
        val settings = loadFromSharedPreferences(uriString)
        
        // 添加到缓存
        cache[uriString] = settings
        
        Log.d(TAG, "从SharedPreferences加载设置: $uriString")
        return settings
    }
    
    /**
     * 设置歌曲自定义名称
     */
    fun setCustomName(uri: Uri, customName: String) {
        if (isDestroyed.get()) return
        
        val uriString = uri.toString()
        val currentSettings = getSongSettings(uri)
        val newSettings = currentSettings.copy(
            customName = customName,
            lastModified = System.currentTimeMillis()
        )
        
        cache[uriString] = newSettings
        dirtyKeys[uriString] = true
        
        Log.d(TAG, "设置自定义名称: $uriString -> $customName")
        
        // 异步写回SharedPreferences
        writeBackAsync(uriString, newSettings)
    }
    
    /**
     * 设置歌曲播放速度
     */
    fun setSpeed(uri: Uri, speed: Float) {
        if (isDestroyed.get()) return
        
        val uriString = uri.toString()
        val currentSettings = getSongSettings(uri)
        val newSettings = currentSettings.copy(
            speed = speed,
            lastModified = System.currentTimeMillis()
        )
        
        cache[uriString] = newSettings
        dirtyKeys[uriString] = true
        
        Log.d(TAG, "设置播放速度: $uriString -> $speed")
        
        writeBackAsync(uriString, newSettings)
    }
    
    /**
     * 设置歌曲音调
     */
    fun setPitch(uri: Uri, pitch: Float) {
        if (isDestroyed.get()) return
        
        val uriString = uri.toString()
        val currentSettings = getSongSettings(uri)
        val newSettings = currentSettings.copy(
            pitch = pitch,
            lastModified = System.currentTimeMillis()
        )
        
        cache[uriString] = newSettings
        dirtyKeys[uriString] = true
        
        Log.d(TAG, "设置音调: $uriString -> $pitch")
        
        writeBackAsync(uriString, newSettings)
    }
    
    /**
     * 设置歌曲裁剪范围
     */
    fun setClipRange(uri: Uri, startMs: Int, endMs: Int) {
        if (isDestroyed.get()) return
        
        val uriString = uri.toString()
        val currentSettings = getSongSettings(uri)
        val newSettings = currentSettings.copy(
            startMs = startMs,
            endMs = endMs,
            lastModified = System.currentTimeMillis()
        )
        
        cache[uriString] = newSettings
        dirtyKeys[uriString] = true
        
        Log.d(TAG, "设置裁剪范围: $uriString -> $startMs-$endMs")
        
        writeBackAsync(uriString, newSettings)
    }
    
    /**
     * 批量预加载歌曲设置
     */
    fun preloadSettings(uris: List<Uri>) {
        if (isDestroyed.get()) return
        
        scope.launch {
            Log.d(TAG, "批量预加载 ${uris.size} 首歌曲设置")
            
            uris.forEach { uri ->
                if (!isDestroyed.get()) {
                    getSongSettings(uri) // 这会触发加载和缓存
                }
            }
        }
    }
    
    /**
     * 强制写回所有脏数据
     */
    fun flushAll() {
        if (isDestroyed.get()) return
        
        scope.launch {
            Log.d(TAG, "强制写回所有脏数据")
            
            val editor = sharedPreferences.edit()
            var hasChanges = false
            
            dirtyKeys.keys.forEach { uriString ->
                cache[uriString]?.let { settings ->
                    writeSettingsToEditor(editor, uriString, settings)
                    hasChanges = true
                }
            }
            
            if (hasChanges) {
                editor.apply()
                dirtyKeys.clear()
                Log.d(TAG, "脏数据写回完成")
            }
        }
    }
    
    /**
     * 清理过期缓存
     */
    fun cleanupExpiredCache() {
        if (isDestroyed.get()) return
        
        val currentTime = System.currentTimeMillis()
        val expiredKeys = mutableListOf<String>()
        
        cache.forEach { (key, settings) ->
            if (currentTime - settings.lastModified > CACHE_EXPIRE_TIME) {
                expiredKeys.add(key)
            }
        }
        
        expiredKeys.forEach { key ->
            cache.remove(key)
            dirtyKeys.remove(key)
        }
        
        if (expiredKeys.isNotEmpty()) {
            Log.d(TAG, "清理过期缓存: ${expiredKeys.size} 项")
        }
        
        // 如果缓存过大，清理最旧的数据
        if (cache.size > MAX_CACHE_SIZE) {
            val sortedEntries = cache.entries.sortedBy { it.value.lastModified }
            val toRemove = sortedEntries.take(cache.size - MAX_CACHE_SIZE)
            
            toRemove.forEach { entry ->
                cache.remove(entry.key)
                dirtyKeys.remove(entry.key)
            }
            
            Log.d(TAG, "清理最旧缓存: ${toRemove.size} 项")
        }
    }
    
    /**
     * 获取缓存状态信息
     */
    fun getCacheStatus(): String {
        return "缓存数量: ${cache.size}, 脏数据: ${dirtyKeys.size}"
    }
    
    /**
     * 销毁缓存管理器
     */
    fun destroy() {
        if (isDestroyed.compareAndSet(false, true)) {
            Log.d(TAG, "销毁设置缓存管理器")
            
            // 停止清理任务
            cleanupJob?.cancel()
            
            // 强制写回所有脏数据
            runBlocking {
                flushAll()
            }
            
            // 取消所有协程
            scope.cancel()
            
            // 清理缓存
            cache.clear()
            dirtyKeys.clear()
        }
    }
    
    // 私有方法
    
    private fun loadFromSharedPreferences(uriString: String): SongSettings {
        return SongSettings(
            customName = sharedPreferences.getString("${uriString}_custom_name", "") ?: "",
            speed = sharedPreferences.getFloat("${uriString}_speed", 1.0f),
            pitch = sharedPreferences.getFloat("${uriString}_pitch", 1.0f),
            startMs = sharedPreferences.getInt("${uriString}_start_ms", 0),
            endMs = sharedPreferences.getInt("${uriString}_end_ms", 0),
            lastModified = System.currentTimeMillis()
        )
    }
    
    private fun writeBackAsync(uriString: String, settings: SongSettings) {
        scope.launch {
            delay(1000) // 延迟1秒写回，避免频繁IO
            
            if (!isDestroyed.get() && dirtyKeys.containsKey(uriString)) {
                val editor = sharedPreferences.edit()
                writeSettingsToEditor(editor, uriString, settings)
                editor.apply()
                
                dirtyKeys.remove(uriString)
                Log.d(TAG, "异步写回设置: $uriString")
            }
        }
    }
    
    private fun writeSettingsToEditor(editor: SharedPreferences.Editor, uriString: String, settings: SongSettings) {
        editor.putString("${uriString}_custom_name", settings.customName)
        editor.putFloat("${uriString}_speed", settings.speed)
        editor.putFloat("${uriString}_pitch", settings.pitch)
        editor.putInt("${uriString}_start_ms", settings.startMs)
        editor.putInt("${uriString}_end_ms", settings.endMs)
    }
    
    private fun startCleanupTask() {
        cleanupJob = scope.launch {
            while (!isDestroyed.get()) {
                delay(CACHE_CLEANUP_INTERVAL)
                cleanupExpiredCache()
            }
        }
    }
}
