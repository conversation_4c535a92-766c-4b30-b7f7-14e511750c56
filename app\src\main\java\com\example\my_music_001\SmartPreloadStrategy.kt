package com.example.my_music_001

import android.net.Uri
import android.util.Log
import kotlin.random.Random

/**
 * 播放模式枚举
 */
enum class PlayMode(val value: Int) {
    SEQUENTIAL(0),  // 顺序播放
    LOOP(1),        // 循环播放
    RANDOM(2)       // 随机播放
}

/**
 * 智能预加载策略管理器
 * 根据播放模式智能预加载相应的歌曲，优化内存使用
 */
class SmartPreloadStrategy {
    companion object {
        private const val TAG = "SmartPreloadStrategy"
        private const val MAX_PRELOAD_COUNT = 3
        private const val RANDOM_HISTORY_SIZE = 10 // 随机播放历史记录大小
    }

    // 随机播放历史记录，避免重复播放
    private val randomHistory = mutableListOf<Int>()

    /**
     * 根据播放模式获取需要预加载的歌曲索引列表
     */
    fun getPreloadIndices(
        currentIndex: Int,
        musicList: List<Uri>,
        playMode: PlayMode
    ): List<Int> {
        if (musicList.isEmpty()) return emptyList()

        return when (playMode) {
            PlayMode.SEQUENTIAL -> getSequentialPreloadIndices(currentIndex, musicList.size)
            PlayMode.LOOP -> getLoopPreloadIndices(currentIndex, musicList.size)
            PlayMode.RANDOM -> getRandomPreloadIndices(currentIndex, musicList.size)
        }
    }

    /**
     * 获取需要预加载的歌曲URI列表
     */
    fun getPreloadUris(
        currentIndex: Int,
        musicList: List<Uri>,
        playMode: PlayMode
    ): List<Uri> {
        val indices = getPreloadIndices(currentIndex, musicList, playMode)
        return indices.mapNotNull { index ->
            if (index in musicList.indices) musicList[index] else null
        }
    }

    /**
     * 更新随机播放历史
     */
    fun updateRandomHistory(index: Int) {
        randomHistory.add(index)
        
        // 保持历史记录大小
        while (randomHistory.size > RANDOM_HISTORY_SIZE) {
            randomHistory.removeAt(0)
        }
        
        Log.d(TAG, "更新随机播放历史: $index, 历史大小: ${randomHistory.size}")
    }

    /**
     * 清空随机播放历史
     */
    fun clearRandomHistory() {
        randomHistory.clear()
        Log.d(TAG, "清空随机播放历史")
    }

    /**
     * 获取下一首歌曲索引（用于切歌逻辑）
     */
    fun getNextIndex(
        currentIndex: Int,
        musicListSize: Int,
        playMode: PlayMode
    ): Int {
        if (musicListSize == 0) return 0

        return when (playMode) {
            PlayMode.SEQUENTIAL -> (currentIndex + 1) % musicListSize
            PlayMode.LOOP -> currentIndex // 循环播放当前歌曲
            PlayMode.RANDOM -> getRandomNextIndex(currentIndex, musicListSize)
        }
    }

    /**
     * 获取上一首歌曲索引（用于切歌逻辑）
     */
    fun getPreviousIndex(
        currentIndex: Int,
        musicListSize: Int,
        playMode: PlayMode
    ): Int {
        if (musicListSize == 0) return 0

        return when (playMode) {
            PlayMode.SEQUENTIAL -> if (currentIndex - 1 < 0) musicListSize - 1 else currentIndex - 1
            PlayMode.LOOP -> currentIndex // 循环播放当前歌曲
            PlayMode.RANDOM -> getRandomPreviousIndex(currentIndex, musicListSize)
        }
    }

    /**
     * 根据播放模式优化预加载策略
     */
    fun optimizePreloadStrategy(
        currentIndex: Int,
        musicList: List<Uri>,
        playMode: PlayMode,
        recentlyPlayedIndices: List<Int> = emptyList()
    ): List<Int> {
        val baseIndices = getPreloadIndices(currentIndex, musicList, playMode)
        
        // 根据最近播放记录调整预加载优先级
        return if (recentlyPlayedIndices.isNotEmpty()) {
            baseIndices.sortedBy { index ->
                val recentlyPlayedPriority = if (index in recentlyPlayedIndices) {
                    recentlyPlayedIndices.indexOf(index)
                } else {
                    Int.MAX_VALUE
                }
                recentlyPlayedPriority
            }
        } else {
            baseIndices
        }
    }

    // 私有方法

    /**
     * 顺序播放模式的预加载策略
     */
    private fun getSequentialPreloadIndices(currentIndex: Int, musicListSize: Int): List<Int> {
        val indices = mutableListOf<Int>()
        
        // 预加载接下来的几首歌曲
        for (i in 1..MAX_PRELOAD_COUNT) {
            val nextIndex = (currentIndex + i) % musicListSize
            indices.add(nextIndex)
        }
        
        Log.d(TAG, "顺序播放预加载策略: 当前=$currentIndex, 预加载=$indices")
        return indices
    }

    /**
     * 循环播放模式的预加载策略
     */
    private fun getLoopPreloadIndices(currentIndex: Int, musicListSize: Int): List<Int> {
        // 循环播放只需要预加载当前歌曲
        Log.d(TAG, "循环播放预加载策略: 当前=$currentIndex, 预加载=[$currentIndex]")
        return listOf(currentIndex)
    }

    /**
     * 随机播放模式的预加载策略
     */
    private fun getRandomPreloadIndices(currentIndex: Int, musicListSize: Int): List<Int> {
        val indices = mutableListOf<Int>()
        
        // 生成随机索引，避免重复最近播放的歌曲
        val availableIndices = (0 until musicListSize).filter { index ->
            index != currentIndex && index !in randomHistory.takeLast(5)
        }
        
        if (availableIndices.isNotEmpty()) {
            val shuffled = availableIndices.shuffled()
            indices.addAll(shuffled.take(MAX_PRELOAD_COUNT))
        } else {
            // 如果没有可用的索引，随机选择
            for (i in 1..MAX_PRELOAD_COUNT) {
                var randomIndex: Int
                do {
                    randomIndex = Random.nextInt(musicListSize)
                } while (randomIndex == currentIndex && musicListSize > 1)
                
                if (randomIndex !in indices) {
                    indices.add(randomIndex)
                }
            }
        }
        
        Log.d(TAG, "随机播放预加载策略: 当前=$currentIndex, 预加载=$indices, 历史=${randomHistory.takeLast(5)}")
        return indices
    }

    /**
     * 获取随机模式下的下一首歌曲索引
     */
    private fun getRandomNextIndex(currentIndex: Int, musicListSize: Int): Int {
        if (musicListSize <= 1) return currentIndex

        // 避免重复最近播放的歌曲
        val availableIndices = (0 until musicListSize).filter { index ->
            index != currentIndex && index !in randomHistory.takeLast(3)
        }

        val nextIndex = if (availableIndices.isNotEmpty()) {
            availableIndices.random()
        } else {
            // 如果没有可用的索引，随机选择一个不是当前的索引
            var randomIndex: Int
            do {
                randomIndex = Random.nextInt(musicListSize)
            } while (randomIndex == currentIndex)
            randomIndex
        }

        updateRandomHistory(nextIndex)
        Log.d(TAG, "随机播放下一首: $currentIndex -> $nextIndex")
        return nextIndex
    }

    /**
     * 获取随机模式下的上一首歌曲索引
     */
    private fun getRandomPreviousIndex(currentIndex: Int, musicListSize: Int): Int {
        if (musicListSize <= 1) return currentIndex

        // 如果有历史记录，返回上一首
        if (randomHistory.size >= 2) {
            val previousIndex = randomHistory[randomHistory.size - 2]
            // 移除当前和之后的历史记录
            val currentHistoryIndex = randomHistory.lastIndexOf(currentIndex)
            if (currentHistoryIndex >= 0) {
                while (randomHistory.size > currentHistoryIndex) {
                    randomHistory.removeAt(randomHistory.size - 1)
                }
            }
            Log.d(TAG, "随机播放上一首(历史): $currentIndex -> $previousIndex")
            return previousIndex
        }

        // 如果没有历史记录，随机选择
        val nextIndex = getRandomNextIndex(currentIndex, musicListSize)
        Log.d(TAG, "随机播放上一首(随机): $currentIndex -> $nextIndex")
        return nextIndex
    }
}
