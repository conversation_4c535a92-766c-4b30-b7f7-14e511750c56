package com.example.my_music_001

import org.junit.After
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test

/**
 * 切歌防抖管理器测试
 */
class SongSwitchDebouncerTest {

    private lateinit var debouncer: SongSwitchDebouncer

    @Before
    fun setUp() {
        debouncer = SongSwitchDebouncer()
    }

    @After
    fun tearDown() {
        debouncer.reset()
    }

    @Test
    fun testBasicSwitching() {
        // 第一次切歌应该成功
        assertTrue("第一次切歌应该成功", debouncer.startSwitch(true))
        
        // 立即再次切歌应该被拒绝
        assertFalse("立即再次切歌应该被拒绝", debouncer.startSwitch(true))
        
        // 完成切歌
        debouncer.finishSwitch()
        
        // 等待防抖时间后应该可以再次切歌
        Thread.sleep(600) // 等待超过500ms防抖时间
        assertTrue("等待防抖时间后应该可以再次切歌", debouncer.startSwitch(true))
        debouncer.finishSwitch()
    }

    @Test
    fun testDirectionSwitching() {
        // 下一首
        assertTrue("下一首应该成功", debouncer.startSwitch(true))
        debouncer.finishSwitch()
        
        Thread.sleep(600)
        
        // 上一首
        assertTrue("上一首应该成功", debouncer.startSwitch(false))
        debouncer.finishSwitch()
    }

    @Test
    fun testConsecutiveSameDirec tion() {
        // 第一次下一首
        assertTrue("第一次下一首应该成功", debouncer.startSwitch(true))
        debouncer.finishSwitch()
        
        Thread.sleep(600)
        
        // 第二次下一首
        assertTrue("第二次下一首应该成功", debouncer.startSwitch(true))
        debouncer.finishSwitch()
        
        Thread.sleep(600)
        
        // 第三次下一首
        assertTrue("第三次下一首应该成功", debouncer.startSwitch(true))
        debouncer.finishSwitch()
        
        Thread.sleep(600)
        
        // 第四次下一首
        assertTrue("第四次下一首应该成功", debouncer.startSwitch(true))
        debouncer.finishSwitch()
        
        // 第五次下一首（连续过多，应该被拒绝）
        Thread.sleep(600)
        assertFalse("连续切歌过多应该被拒绝", debouncer.startSwitch(true))
    }

    @Test
    fun testForceStop() {
        // 第一次强制停止应该返回true
        assertTrue("第一次强制停止应该返回true", debouncer.shouldForceStop())
        
        // 立即再次强制停止应该返回false
        assertFalse("立即再次强制停止应该返回false", debouncer.shouldForceStop())
        
        // 等待间隔后应该可以再次强制停止
        Thread.sleep(150)
        assertTrue("等待间隔后应该可以再次强制停止", debouncer.shouldForceStop())
    }

    @Test
    fun testSwitchingState() {
        // 初始状态应该不在切歌中
        assertFalse("初始状态应该不在切歌中", debouncer.isSwitchingNow())
        
        // 开始切歌后应该在切歌中
        assertTrue("开始切歌应该成功", debouncer.startSwitch(true))
        assertTrue("开始切歌后应该在切歌中", debouncer.isSwitchingNow())
        
        // 完成切歌后应该不在切歌中
        debouncer.finishSwitch()
        assertFalse("完成切歌后应该不在切歌中", debouncer.isSwitchingNow())
    }

    @Test
    fun testRemainingDebounceTime() {
        // 初始状态剩余时间应该为0
        assertEquals("初始状态剩余时间应该为0", 0L, debouncer.getRemainingDebounceTime())
        
        // 开始切歌后应该有剩余时间
        assertTrue("开始切歌应该成功", debouncer.startSwitch(true))
        debouncer.finishSwitch()
        
        val remainingTime = debouncer.getRemainingDebounceTime()
        assertTrue("应该有剩余防抖时间", remainingTime > 0)
        assertTrue("剩余时间应该小于等于500ms", remainingTime <= 500)
    }

    @Test
    fun testReset() {
        // 开始切歌
        assertTrue("开始切歌应该成功", debouncer.startSwitch(true))
        assertTrue("应该在切歌中", debouncer.isSwitchingNow())
        
        // 重置状态
        debouncer.reset()
        
        // 重置后应该可以立即切歌
        assertFalse("重置后应该不在切歌中", debouncer.isSwitchingNow())
        assertTrue("重置后应该可以立即切歌", debouncer.startSwitch(true))
        debouncer.finishSwitch()
    }

    @Test
    fun testStatus() {
        val status = debouncer.getStatus()
        assertNotNull("状态信息不应该为空", status)
        assertTrue("状态信息应该包含切歌状态", status.contains("切歌状态"))
    }

    @Test
    fun testRapidClicking() {
        // 模拟快速连续点击
        var successCount = 0
        var rejectedCount = 0
        
        for (i in 1..10) {
            if (debouncer.startSwitch(true)) {
                successCount++
                debouncer.finishSwitch()
            } else {
                rejectedCount++
            }
            Thread.sleep(50) // 50ms间隔，远小于防抖时间
        }
        
        // 应该只有第一次成功，其余都被拒绝
        assertEquals("应该只有第一次成功", 1, successCount)
        assertEquals("其余9次应该被拒绝", 9, rejectedCount)
    }

    @Test
    fun testAlternatingDirection() {
        // 交替方向切歌应该重置连续计数
        for (i in 1..6) {
            val isNext = i % 2 == 1
            assertTrue("交替方向切歌第${i}次应该成功", debouncer.startSwitch(isNext))
            debouncer.finishSwitch()
            Thread.sleep(600) // 等待防抖时间
        }
    }

    @Test
    fun testConcurrentAccess() {
        // 测试并发访问（简单模拟）
        val thread1 = Thread {
            debouncer.startSwitch(true)
            Thread.sleep(100)
            debouncer.finishSwitch()
        }
        
        val thread2 = Thread {
            Thread.sleep(50)
            debouncer.startSwitch(false) // 应该被拒绝
        }
        
        thread1.start()
        thread2.start()
        
        thread1.join()
        thread2.join()
        
        // 验证状态正常
        assertFalse("并发测试后应该不在切歌中", debouncer.isSwitchingNow())
    }

    @Test
    fun testPerformance() {
        val startTime = System.currentTimeMillis()
        
        // 执行1000次防抖检查
        repeat(1000) {
            debouncer.canSwitch(true)
        }
        
        val endTime = System.currentTimeMillis()
        val duration = endTime - startTime
        
        // 1000次检查应该在100ms内完成
        assertTrue("性能测试：1000次检查应该在100ms内完成，实际耗时：${duration}ms", duration < 100)
    }
}
