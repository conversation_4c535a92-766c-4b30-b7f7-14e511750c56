package com.example.my_music_001

import android.content.Context
import android.media.MediaPlayer
import android.net.Uri
import android.util.Log
import kotlinx.coroutines.*
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicBoolean

/**
 * 音乐预加载管理器
 * 管理多个MediaPlayer实例，实现歌曲预加载和无缝切换
 */
class MusicPreloadManager(private val context: Context) {
    companion object {
        private const val TAG = "MusicPreloadManager"
        private const val MAX_PRELOAD_COUNT = 3 // 最多预加载3首歌曲
    }

    // 当前播放的MediaPlayer
    private var currentPlayer: MediaPlayer? = null
    
    // 预加载的MediaPlayer池
    private val preloadedPlayers = ConcurrentHashMap<String, MediaPlayer>()
    
    // 正在预加载的任务
    private val preloadingTasks = ConcurrentHashMap<String, Job>()
    
    // 协程作用域
    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    // 是否已销毁
    private val isDestroyed = AtomicBoolean(false)
    
    // 播放完成监听器
    private var onCompletionListener: (() -> Unit)? = null
    
    // 播放准备完成监听器
    private var onPreparedListener: ((MediaPlayer) -> Unit)? = null

    /**
     * 设置当前播放的歌曲
     */
    fun setCurrentSong(uri: Uri): MediaPlayer? {
        if (isDestroyed.get()) return null
        
        val uriString = uri.toString()
        Log.d(TAG, "设置当前播放歌曲: $uriString")
        
        // 检查是否已经预加载
        val preloadedPlayer = preloadedPlayers.remove(uriString)
        if (preloadedPlayer != null) {
            Log.d(TAG, "使用预加载的播放器")
            // 释放当前播放器
            currentPlayer?.release()
            currentPlayer = preloadedPlayer
            
            // 设置监听器
            setupPlayerListeners(currentPlayer!!)
            return currentPlayer
        }
        
        // 如果没有预加载，创建新的播放器
        Log.d(TAG, "创建新的播放器")
        return createNewPlayer(uri)
    }
    
    /**
     * 预加载指定歌曲
     */
    fun preloadSong(uri: Uri) {
        if (isDestroyed.get()) return
        
        val uriString = uri.toString()
        
        // 如果已经预加载或正在预加载，跳过
        if (preloadedPlayers.containsKey(uriString) || preloadingTasks.containsKey(uriString)) {
            Log.d(TAG, "歌曲已预加载或正在预加载: $uriString")
            return
        }
        
        // 检查预加载数量限制
        if (preloadedPlayers.size >= MAX_PRELOAD_COUNT) {
            Log.d(TAG, "预加载数量已达上限，清理最旧的预加载")
            clearOldestPreload()
        }
        
        Log.d(TAG, "开始预加载歌曲: $uriString")
        
        val job = scope.launch {
            try {
                val player = MediaPlayer()
                player.setDataSource(context, uri)
                
                // 异步准备
                player.prepareAsync()
                
                // 等待准备完成
                val prepared = CompletableDeferred<Boolean>()
                player.setOnPreparedListener {
                    Log.d(TAG, "预加载完成: $uriString")
                    prepared.complete(true)
                }
                player.setOnErrorListener { _, what, extra ->
                    Log.e(TAG, "预加载失败: $uriString, what=$what, extra=$extra")
                    prepared.complete(false)
                    true
                }
                
                if (prepared.await()) {
                    // 预加载成功，添加到池中
                    preloadedPlayers[uriString] = player
                    Log.d(TAG, "预加载成功并添加到池中: $uriString")
                } else {
                    // 预加载失败，释放资源
                    player.release()
                }
            } catch (e: Exception) {
                Log.e(TAG, "预加载异常: $uriString", e)
            } finally {
                preloadingTasks.remove(uriString)
            }
        }
        
        preloadingTasks[uriString] = job
    }
    
    /**
     * 批量预加载歌曲
     */
    fun preloadSongs(uris: List<Uri>) {
        if (isDestroyed.get()) return
        
        Log.d(TAG, "批量预加载 ${uris.size} 首歌曲")
        uris.forEach { uri ->
            preloadSong(uri)
        }
    }
    
    /**
     * 清理指定歌曲的预加载
     */
    fun clearPreload(uri: Uri) {
        val uriString = uri.toString()
        
        // 取消预加载任务
        preloadingTasks[uriString]?.cancel()
        preloadingTasks.remove(uriString)
        
        // 释放预加载的播放器
        preloadedPlayers.remove(uriString)?.release()
        
        Log.d(TAG, "清理预加载: $uriString")
    }
    
    /**
     * 清理所有预加载
     */
    fun clearAllPreloads() {
        Log.d(TAG, "清理所有预加载")
        
        // 取消所有预加载任务
        preloadingTasks.values.forEach { it.cancel() }
        preloadingTasks.clear()
        
        // 释放所有预加载的播放器
        preloadedPlayers.values.forEach { it.release() }
        preloadedPlayers.clear()
    }
    
    /**
     * 获取当前播放器
     */
    fun getCurrentPlayer(): MediaPlayer? = currentPlayer
    
    /**
     * 设置播放完成监听器
     */
    fun setOnCompletionListener(listener: () -> Unit) {
        onCompletionListener = listener
        currentPlayer?.setOnCompletionListener { listener() }
    }
    
    /**
     * 设置播放准备完成监听器
     */
    fun setOnPreparedListener(listener: (MediaPlayer) -> Unit) {
        onPreparedListener = listener
        currentPlayer?.setOnPreparedListener { listener(it) }
    }
    
    /**
     * 销毁管理器
     */
    fun destroy() {
        if (isDestroyed.compareAndSet(false, true)) {
            Log.d(TAG, "销毁预加载管理器")
            
            // 取消所有协程
            scope.cancel()
            
            // 释放当前播放器
            currentPlayer?.release()
            currentPlayer = null
            
            // 清理所有预加载
            clearAllPreloads()
        }
    }
    
    /**
     * 获取预加载状态信息
     */
    fun getPreloadStatus(): String {
        return "预加载中: ${preloadingTasks.size}, 已预加载: ${preloadedPlayers.size}"
    }
    
    // 私有方法
    
    private fun createNewPlayer(uri: Uri): MediaPlayer? {
        return try {
            val player = MediaPlayer()
            player.setDataSource(context, uri)
            setupPlayerListeners(player)
            
            // 释放旧的播放器
            currentPlayer?.release()
            currentPlayer = player
            
            player
        } catch (e: Exception) {
            Log.e(TAG, "创建播放器失败", e)
            null
        }
    }
    
    private fun setupPlayerListeners(player: MediaPlayer) {
        player.setOnCompletionListener {
            onCompletionListener?.invoke()
        }
        
        player.setOnPreparedListener {
            onPreparedListener?.invoke(it)
        }
        
        player.setOnErrorListener { _, what, extra ->
            Log.e(TAG, "播放器错误: what=$what, extra=$extra")
            false
        }
    }
    
    private fun clearOldestPreload() {
        if (preloadedPlayers.isNotEmpty()) {
            val oldestKey = preloadedPlayers.keys.first()
            preloadedPlayers.remove(oldestKey)?.release()
            Log.d(TAG, "清理最旧的预加载: $oldestKey")
        }
    }
}
