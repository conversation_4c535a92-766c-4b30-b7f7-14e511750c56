package com.example.my_music_001

import android.content.Context
import android.media.MediaPlayer
import android.net.Uri
import android.util.Log
import kotlinx.coroutines.*
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicReference

/**
 * 高性能快速切歌管理器
 * 实现多MediaPlayer实例预加载，无缝切换
 */
class FastSwitchManager(private val context: Context) {
    
    companion object {
        private const val TAG = "FastSwitchManager"
        private const val MAX_PRELOAD_COUNT = 3 // 最多预加载3首歌曲
    }
    
    // 当前播放的MediaPlayer
    private val currentPlayer = AtomicReference<MediaPlayer?>(null)
    
    // 预加载的MediaPlayer池
    private val preloadedPlayers = ConcurrentHashMap<String, MediaPlayer>()
    
    // 歌曲设置缓存
    private val songSettingsCache = ConcurrentHashMap<String, SongSettings>()
    
    // 后台协程作用域
    private val backgroundScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    // 是否正在切歌
    private val isSwitching = AtomicBoolean(false)

    // 当前切歌的URI，用于防止状态混乱
    private val currentSwitchingUri = AtomicReference<String?>(null)

    // 切歌序列号，用于识别最新的切歌操作
    private val switchSequence = AtomicReference(0L)

    // 回调接口
    private var onPreparedListener: ((MediaPlayer) -> Unit)? = null
    private var onCompletionListener: (() -> Unit)? = null
    private var onErrorListener: ((MediaPlayer?, Int, Int) -> Boolean)? = null
    
    /**
     * 歌曲设置数据类
     */
    data class SongSettings(
        var skipStartMs: Int = 0,
        var skipEndMs: Int = 0,
        var originalDuration: Int = 0,
        var effectiveDuration: Int = 0
    )
    
    /**
     * 设置回调监听器
     */
    fun setOnPreparedListener(listener: (MediaPlayer) -> Unit) {
        onPreparedListener = listener
    }
    
    fun setOnCompletionListener(listener: () -> Unit) {
        onCompletionListener = listener
    }
    
    fun setOnErrorListener(listener: (MediaPlayer?, Int, Int) -> Boolean) {
        onErrorListener = listener
    }
    
    /**
     * 预加载歌曲列表
     */
    fun preloadSongs(currentIndex: Int, musicList: List<Uri>) {
        if (musicList.isEmpty()) return
        
        backgroundScope.launch {
            try {
                // 计算需要预加载的歌曲索引
                val indicesToPreload = mutableSetOf<Int>()
                
                // 预加载下一首
                val nextIndex = (currentIndex + 1) % musicList.size
                indicesToPreload.add(nextIndex)
                
                // 预加载上一首
                val prevIndex = if (currentIndex - 1 < 0) musicList.size - 1 else currentIndex - 1
                indicesToPreload.add(prevIndex)
                
                // 如果歌曲数量足够，再预加载下下一首
                if (musicList.size > 3) {
                    val nextNextIndex = (currentIndex + 2) % musicList.size
                    indicesToPreload.add(nextNextIndex)
                }
                
                // 清理不需要的预加载
                cleanupUnusedPreloads(indicesToPreload.map { musicList[it].toString() }.toSet())
                
                // 开始预加载
                indicesToPreload.forEach { index ->
                    if (index < musicList.size) {
                        preloadSong(musicList[index])
                    }
                }
                
                Log.d(TAG, "预加载完成，当前索引: $currentIndex, 预加载索引: $indicesToPreload")
            } catch (e: Exception) {
                Log.e(TAG, "预加载歌曲时出错", e)
            }
        }
    }
    
    /**
     * 预加载单首歌曲
     */
    private suspend fun preloadSong(uri: Uri) {
        val uriString = uri.toString()
        
        // 如果已经预加载过，跳过
        if (preloadedPlayers.containsKey(uriString)) {
            return
        }
        
        try {
            val player = MediaPlayer()
            
            // 设置数据源
            player.setDataSource(context, uri)
            
            // 异步准备
            val prepareDeferred = CompletableDeferred<Boolean>()
            
            player.setOnPreparedListener {
                Log.d(TAG, "歌曲预加载完成: $uriString")
                prepareDeferred.complete(true)
            }
            
            player.setOnErrorListener { _, what, extra ->
                Log.e(TAG, "预加载歌曲出错: $uriString, what=$what, extra=$extra")
                prepareDeferred.complete(false)
                true
            }
            
            player.prepareAsync()
            
            // 等待准备完成
            if (prepareDeferred.await()) {
                preloadedPlayers[uriString] = player
                
                // 缓存歌曲设置
                cacheSongSettings(uriString, player.duration)
                
                Log.d(TAG, "成功预加载歌曲: $uriString")
            } else {
                player.release()
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "预加载歌曲失败: $uriString", e)
        }
    }
    
    /**
     * 缓存歌曲设置
     */
    private fun cacheSongSettings(uriString: String, duration: Int) {
        try {
            val sharedPreferences = context.getSharedPreferences("music_settings", Context.MODE_PRIVATE)
            
            val settings = SongSettings(
                skipStartMs = sharedPreferences.getInt("${uriString}_start_ms", 0),
                skipEndMs = sharedPreferences.getInt("${uriString}_end_ms", 0),
                originalDuration = duration,
                effectiveDuration = duration
            )
            
            // 计算有效时长
            if (settings.skipStartMs > 0 || (settings.skipEndMs > 0 && settings.skipEndMs < duration)) {
                val startMs = settings.skipStartMs
                val endMs = if (settings.skipEndMs > 0 && settings.skipEndMs < duration) settings.skipEndMs else duration
                settings.effectiveDuration = endMs - startMs
            }
            
            songSettingsCache[uriString] = settings
            Log.d(TAG, "缓存歌曲设置: $uriString, 有效时长: ${settings.effectiveDuration}ms")
            
        } catch (e: Exception) {
            Log.e(TAG, "缓存歌曲设置失败: $uriString", e)
        }
    }
    
    /**
     * 快速切换到指定歌曲
     */
    fun fastSwitchTo(uri: Uri, autoPlay: Boolean = true): Boolean {
        val uriString = uri.toString()
        val currentSequence = switchSequence.incrementAndGet()

        // 检查是否正在切歌，如果是则取消之前的操作
        if (!isSwitching.compareAndSet(false, true)) {
            Log.d(TAG, "正在切歌中，取消之前的操作并开始新的切歌")
            // 强制停止当前播放，防止多首歌曲同时播放
            forceStopAllPlayers()
        }

        try {
            // 设置当前切歌的URI和序列号
            currentSwitchingUri.set(uriString)

            Log.d(TAG, "开始快速切换: $uriString, 序列号: $currentSequence")

            // 立即停止所有播放，防止重叠
            forceStopAllPlayers()

            // 尝试使用预加载的播放器
            val preloadedPlayer = preloadedPlayers.remove(uriString)

            if (preloadedPlayer != null) {
                // 再次检查是否还是最新的切歌操作
                if (currentSwitchingUri.get() != uriString) {
                    Log.d(TAG, "切歌操作已被新的操作覆盖，放弃当前操作")
                    preloadedPlayer.release()
                    return false
                }

                Log.d(TAG, "使用预加载播放器快速切换: $uriString")

                // 设置为当前播放器
                currentPlayer.set(preloadedPlayer)

                // 设置监听器
                setupPlayerListeners(preloadedPlayer)

                // 应用歌曲设置
                applySongSettings(preloadedPlayer, uriString)

                // 开始播放
                if (autoPlay) {
                    preloadedPlayer.start()
                }

                Log.d(TAG, "快速切换成功: $uriString")
                return true

            } else {
                Log.d(TAG, "没有预加载播放器，使用普通方式: $uriString")
                return false
            }

        } catch (e: Exception) {
            Log.e(TAG, "快速切换失败", e)
            return false
        } finally {
            isSwitching.set(false)
        }
    }

    /**
     * 强制停止所有播放器，防止多首歌曲同时播放
     */
    private fun forceStopAllPlayers() {
        try {
            // 停止当前播放器
            currentPlayer.get()?.let { player ->
                try {
                    if (player.isPlaying) {
                        player.stop()
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "停止当前播放器时出错", e)
                }
            }

            // 停止所有预加载播放器
            preloadedPlayers.values.forEach { player ->
                try {
                    if (player.isPlaying) {
                        player.stop()
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "停止预加载播放器时出错", e)
                }
            }

            Log.d(TAG, "强制停止所有播放器完成")
        } catch (e: Exception) {
            Log.e(TAG, "强制停止所有播放器时出错", e)
        }
    }
    
    /**
     * 停止当前播放器
     */
    private fun stopCurrentPlayer() {
        currentPlayer.get()?.let { player ->
            try {
                if (player.isPlaying) {
                    player.stop()
                }
                player.release()
                Log.d(TAG, "停止并释放当前播放器")
            } catch (e: Exception) {
                Log.e(TAG, "停止当前播放器时出错", e)
            }
        }
        currentPlayer.set(null)
    }
    
    /**
     * 设置播放器监听器
     */
    private fun setupPlayerListeners(player: MediaPlayer) {
        player.setOnPreparedListener { mp ->
            onPreparedListener?.invoke(mp)
        }
        
        player.setOnCompletionListener {
            onCompletionListener?.invoke()
        }
        
        player.setOnErrorListener { mp, what, extra ->
            onErrorListener?.invoke(mp, what, extra) ?: false
        }
    }
    
    /**
     * 应用歌曲设置
     */
    private fun applySongSettings(player: MediaPlayer, uriString: String) {
        val settings = songSettingsCache[uriString]
        if (settings != null && settings.skipStartMs > 0) {
            try {
                player.seekTo(settings.skipStartMs)
                Log.d(TAG, "应用歌曲设置，跳转到: ${settings.skipStartMs}ms")
            } catch (e: Exception) {
                Log.e(TAG, "应用歌曲设置失败", e)
            }
        }
    }
    
    /**
     * 清理不需要的预加载
     */
    private fun cleanupUnusedPreloads(keepUris: Set<String>) {
        val toRemove = preloadedPlayers.keys.filter { it !in keepUris }
        
        toRemove.forEach { uriString ->
            preloadedPlayers.remove(uriString)?.let { player ->
                try {
                    player.release()
                    Log.d(TAG, "清理预加载播放器: $uriString")
                } catch (e: Exception) {
                    Log.e(TAG, "清理预加载播放器时出错", e)
                }
            }
        }
    }
    
    /**
     * 获取当前播放器
     */
    fun getCurrentPlayer(): MediaPlayer? = currentPlayer.get()
    
    /**
     * 获取缓存的歌曲设置
     */
    fun getCachedSettings(uri: Uri): SongSettings? = songSettingsCache[uri.toString()]
    
    /**
     * 销毁管理器
     */
    fun destroy() {
        backgroundScope.cancel()
        
        // 停止当前播放器
        stopCurrentPlayer()
        
        // 清理所有预加载播放器
        preloadedPlayers.values.forEach { player ->
            try {
                player.release()
            } catch (e: Exception) {
                Log.e(TAG, "释放预加载播放器时出错", e)
            }
        }
        preloadedPlayers.clear()
        
        // 清理缓存
        songSettingsCache.clear()
        
        Log.d(TAG, "FastSwitchManager已销毁")
    }
}
