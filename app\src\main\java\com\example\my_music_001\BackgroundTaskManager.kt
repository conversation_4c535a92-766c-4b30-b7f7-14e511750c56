package com.example.my_music_001

import android.util.Log
import kotlinx.coroutines.*
import java.util.concurrent.Executors
import java.util.concurrent.atomic.AtomicBoolean
import kotlin.coroutines.CoroutineContext

/**
 * 后台任务管理器
 * 将耗时操作移到后台线程执行，确保主线程流畅
 */
class BackgroundTaskManager {
    companion object {
        private const val TAG = "BackgroundTaskManager"
        private const val IO_THREAD_COUNT = 2
        private const val CPU_THREAD_COUNT = 2
    }

    // 不同类型的线程池
    private val ioDispatcher = Executors.newFixedThreadPool(IO_THREAD_COUNT) { r ->
        Thread(r, "MusicPlayer-IO").apply {
            isDaemon = true
            priority = Thread.NORM_PRIORITY - 1
        }
    }.asCoroutineDispatcher()

    private val cpuDispatcher = Executors.newFixedThreadPool(CPU_THREAD_COUNT) { r ->
        Thread(r, "MusicPlayer-CPU").apply {
            isDaemon = true
            priority = Thread.NORM_PRIORITY
        }
    }.asCoroutineDispatcher()

    // 主协程作用域
    private val scope = CoroutineScope(SupervisorJob())
    
    // 是否已销毁
    private val isDestroyed = AtomicBoolean(false)

    /**
     * 执行IO密集型任务（文件读写、网络请求等）
     */
    fun executeIOTask(
        onStart: (() -> Unit)? = null,
        onComplete: ((Result<Unit>) -> Unit)? = null,
        task: suspend () -> Unit
    ): Job? {
        if (isDestroyed.get()) return null

        return scope.launch(ioDispatcher) {
            try {
                // 在主线程执行开始回调
                onStart?.let {
                    withContext(Dispatchers.Main) { it() }
                }

                Log.d(TAG, "开始执行IO任务")
                task()
                Log.d(TAG, "IO任务执行完成")

                // 在主线程执行完成回调
                onComplete?.let {
                    withContext(Dispatchers.Main) { 
                        it(Result.success(Unit))
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "IO任务执行失败", e)
                onComplete?.let {
                    withContext(Dispatchers.Main) { 
                        it(Result.failure(e))
                    }
                }
            }
        }
    }

    /**
     * 执行CPU密集型任务（音频解码、图像处理等）
     */
    fun executeCPUTask(
        onStart: (() -> Unit)? = null,
        onComplete: ((Result<Unit>) -> Unit)? = null,
        task: suspend () -> Unit
    ): Job? {
        if (isDestroyed.get()) return null

        return scope.launch(cpuDispatcher) {
            try {
                onStart?.let {
                    withContext(Dispatchers.Main) { it() }
                }

                Log.d(TAG, "开始执行CPU任务")
                task()
                Log.d(TAG, "CPU任务执行完成")

                onComplete?.let {
                    withContext(Dispatchers.Main) { 
                        it(Result.success(Unit))
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "CPU任务执行失败", e)
                onComplete?.let {
                    withContext(Dispatchers.Main) { 
                        it(Result.failure(e))
                    }
                }
            }
        }
    }

    /**
     * 执行带返回值的IO任务
     */
    fun <T> executeIOTaskWithResult(
        onStart: (() -> Unit)? = null,
        onComplete: ((Result<T>) -> Unit)? = null,
        task: suspend () -> T
    ): Job? {
        if (isDestroyed.get()) return null

        return scope.launch(ioDispatcher) {
            try {
                onStart?.let {
                    withContext(Dispatchers.Main) { it() }
                }

                Log.d(TAG, "开始执行带返回值的IO任务")
                val result = task()
                Log.d(TAG, "带返回值的IO任务执行完成")

                onComplete?.let {
                    withContext(Dispatchers.Main) { 
                        it(Result.success(result))
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "带返回值的IO任务执行失败", e)
                onComplete?.let {
                    withContext(Dispatchers.Main) { 
                        it(Result.failure(e))
                    }
                }
            }
        }
    }

    /**
     * 执行带返回值的CPU任务
     */
    fun <T> executeCPUTaskWithResult(
        onStart: (() -> Unit)? = null,
        onComplete: ((Result<T>) -> Unit)? = null,
        task: suspend () -> T
    ): Job? {
        if (isDestroyed.get()) return null

        return scope.launch(cpuDispatcher) {
            try {
                onStart?.let {
                    withContext(Dispatchers.Main) { it() }
                }

                Log.d(TAG, "开始执行带返回值的CPU任务")
                val result = task()
                Log.d(TAG, "带返回值的CPU任务执行完成")

                onComplete?.let {
                    withContext(Dispatchers.Main) { 
                        it(Result.success(result))
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "带返回值的CPU任务执行失败", e)
                onComplete?.let {
                    withContext(Dispatchers.Main) { 
                        it(Result.failure(e))
                    }
                }
            }
        }
    }

    /**
     * 延迟执行任务
     */
    fun executeDelayedTask(
        delayMs: Long,
        onComplete: (() -> Unit)? = null,
        task: suspend () -> Unit
    ): Job? {
        if (isDestroyed.get()) return null

        return scope.launch {
            try {
                delay(delayMs)
                task()
                
                onComplete?.let {
                    withContext(Dispatchers.Main) { it() }
                }
            } catch (e: Exception) {
                Log.e(TAG, "延迟任务执行失败", e)
            }
        }
    }

    /**
     * 在主线程执行任务
     */
    fun executeOnMainThread(task: () -> Unit): Job? {
        if (isDestroyed.get()) return null

        return scope.launch(Dispatchers.Main) {
            try {
                task()
            } catch (e: Exception) {
                Log.e(TAG, "主线程任务执行失败", e)
            }
        }
    }

    /**
     * 批量执行IO任务
     */
    fun executeBatchIOTasks(
        tasks: List<suspend () -> Unit>,
        onProgress: ((Int, Int) -> Unit)? = null,
        onComplete: ((List<Result<Unit>>) -> Unit)? = null
    ): Job? {
        if (isDestroyed.get()) return null

        return scope.launch(ioDispatcher) {
            val results = mutableListOf<Result<Unit>>()
            
            tasks.forEachIndexed { index, task ->
                try {
                    task()
                    results.add(Result.success(Unit))
                    
                    onProgress?.let {
                        withContext(Dispatchers.Main) { 
                            it(index + 1, tasks.size)
                        }
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "批量任务第${index + 1}个执行失败", e)
                    results.add(Result.failure(e))
                }
            }
            
            onComplete?.let {
                withContext(Dispatchers.Main) { 
                    it(results)
                }
            }
        }
    }

    /**
     * 获取任务管理器状态
     */
    fun getStatus(): String {
        return if (isDestroyed.get()) {
            "已销毁"
        } else {
            "运行中 - IO线程:$IO_THREAD_COUNT, CPU线程:$CPU_THREAD_COUNT"
        }
    }

    /**
     * 销毁任务管理器
     */
    fun destroy() {
        if (isDestroyed.compareAndSet(false, true)) {
            Log.d(TAG, "销毁后台任务管理器")
            
            // 取消所有协程
            scope.cancel()
            
            // 关闭线程池
            try {
                ioDispatcher.close()
                cpuDispatcher.close()
            } catch (e: Exception) {
                Log.e(TAG, "关闭线程池失败", e)
            }
        }
    }
}
