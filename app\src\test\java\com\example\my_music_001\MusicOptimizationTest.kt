package com.example.my_music_001

import android.content.Context
import android.net.Uri
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import kotlinx.coroutines.delay
import kotlinx.coroutines.runBlocking
import org.junit.After
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith

/**
 * 音乐播放器优化功能测试
 */
@RunWith(AndroidJUnit4::class)
class MusicOptimizationTest {

    private lateinit var context: Context
    private lateinit var musicPreloadManager: MusicPreloadManager
    private lateinit var songSettingsCache: SongSettingsCache
    private lateinit var backgroundTaskManager: BackgroundTaskManager
    private lateinit var smartPreloadStrategy: SmartPreloadStrategy

    private val testUri1 = Uri.parse("content://media/external/audio/media/1")
    private val testUri2 = Uri.parse("content://media/external/audio/media/2")
    private val testUri3 = Uri.parse("content://media/external/audio/media/3")

    @Before
    fun setUp() {
        context = ApplicationProvider.getApplicationContext()
        musicPreloadManager = MusicPreloadManager(context)
        songSettingsCache = SongSettingsCache(context)
        backgroundTaskManager = BackgroundTaskManager()
        smartPreloadStrategy = SmartPreloadStrategy()
    }

    @After
    fun tearDown() {
        musicPreloadManager.destroy()
        songSettingsCache.destroy()
        backgroundTaskManager.destroy()
    }

    @Test
    fun testSongSettingsCache() {
        // 测试设置和获取歌曲自定义名称
        songSettingsCache.setCustomName(testUri1, "测试歌曲1")
        val settings1 = songSettingsCache.getSongSettings(testUri1)
        assertEquals("测试歌曲1", settings1.customName)

        // 测试设置和获取播放速度
        songSettingsCache.setSpeed(testUri1, 1.5f)
        val settings2 = songSettingsCache.getSongSettings(testUri1)
        assertEquals(1.5f, settings2.speed, 0.01f)

        // 测试设置和获取音调
        songSettingsCache.setPitch(testUri1, 1.2f)
        val settings3 = songSettingsCache.getSongSettings(testUri1)
        assertEquals(1.2f, settings3.pitch, 0.01f)

        // 测试设置和获取裁剪范围
        songSettingsCache.setClipRange(testUri1, 1000, 5000)
        val settings4 = songSettingsCache.getSongSettings(testUri1)
        assertEquals(1000, settings4.startMs)
        assertEquals(5000, settings4.endMs)
    }

    @Test
    fun testSongSettingsCachePerformance() = runBlocking {
        val startTime = System.currentTimeMillis()
        
        // 批量设置100个歌曲的设置
        repeat(100) { index ->
            val uri = Uri.parse("content://media/external/audio/media/$index")
            songSettingsCache.setCustomName(uri, "歌曲$index")
            songSettingsCache.setSpeed(uri, 1.0f + index * 0.1f)
            songSettingsCache.setPitch(uri, 1.0f + index * 0.05f)
            songSettingsCache.setClipRange(uri, index * 1000, (index + 1) * 1000)
        }
        
        val setTime = System.currentTimeMillis() - startTime
        
        // 批量获取设置
        val getStartTime = System.currentTimeMillis()
        repeat(100) { index ->
            val uri = Uri.parse("content://media/external/audio/media/$index")
            songSettingsCache.getSongSettings(uri)
        }
        val getTime = System.currentTimeMillis() - getStartTime
        
        println("设置100个歌曲耗时: ${setTime}ms")
        println("获取100个歌曲设置耗时: ${getTime}ms")
        
        // 验证缓存性能：获取时间应该明显小于设置时间
        assertTrue("缓存获取应该比设置更快", getTime < setTime)
    }

    @Test
    fun testSmartPreloadStrategy() {
        val musicList = listOf(testUri1, testUri2, testUri3)
        
        // 测试顺序播放模式
        val sequentialIndices = smartPreloadStrategy.getPreloadIndices(0, musicList, PlayMode.SEQUENTIAL)
        assertTrue("顺序播放应该预加载后续歌曲", sequentialIndices.contains(1))
        
        // 测试循环播放模式
        val loopIndices = smartPreloadStrategy.getPreloadIndices(0, musicList, PlayMode.LOOP)
        assertTrue("循环播放应该预加载当前歌曲", loopIndices.contains(0))
        
        // 测试随机播放模式
        val randomIndices = smartPreloadStrategy.getPreloadIndices(0, musicList, PlayMode.RANDOM)
        assertFalse("随机播放不应该包含当前歌曲", randomIndices.contains(0))
        assertTrue("随机播放应该预加载其他歌曲", randomIndices.isNotEmpty())
    }

    @Test
    fun testRandomPlayHistory() {
        val musicListSize = 5
        
        // 测试随机播放历史记录
        repeat(10) {
            val nextIndex = smartPreloadStrategy.getNextIndex(0, musicListSize, PlayMode.RANDOM)
            assertTrue("随机索引应该在有效范围内", nextIndex in 0 until musicListSize)
            assertNotEquals("随机播放不应该选择当前歌曲", 0, nextIndex)
        }
        
        // 测试历史记录清理
        smartPreloadStrategy.clearRandomHistory()
        val nextIndex = smartPreloadStrategy.getNextIndex(0, musicListSize, PlayMode.RANDOM)
        assertTrue("清理历史后仍应该正常工作", nextIndex in 0 until musicListSize)
    }

    @Test
    fun testBackgroundTaskManager() = runBlocking {
        var ioTaskCompleted = false
        var cpuTaskCompleted = false
        
        // 测试IO任务
        backgroundTaskManager.executeIOTask(
            onComplete = { result ->
                assertTrue("IO任务应该成功完成", result.isSuccess)
                ioTaskCompleted = true
            }
        ) {
            delay(100) // 模拟IO操作
        }
        
        // 测试CPU任务
        backgroundTaskManager.executeCPUTask(
            onComplete = { result ->
                assertTrue("CPU任务应该成功完成", result.isSuccess)
                cpuTaskCompleted = true
            }
        ) {
            // 模拟CPU密集型操作
            var sum = 0
            repeat(1000) { sum += it }
        }
        
        // 等待任务完成
        delay(500)
        
        assertTrue("IO任务应该已完成", ioTaskCompleted)
        assertTrue("CPU任务应该已完成", cpuTaskCompleted)
    }

    @Test
    fun testBackgroundTaskManagerWithResult() = runBlocking {
        var result: String? = null
        
        backgroundTaskManager.executeIOTaskWithResult<String>(
            onComplete = { taskResult ->
                if (taskResult.isSuccess) {
                    result = taskResult.getOrNull()
                }
            }
        ) {
            delay(100)
            "测试结果"
        }
        
        delay(300)
        assertEquals("应该返回正确的结果", "测试结果", result)
    }

    @Test
    fun testPreloadManagerStatus() {
        val status = musicPreloadManager.getPreloadStatus()
        assertNotNull("预加载状态不应该为空", status)
        assertTrue("状态应该包含预加载信息", status.contains("预加载"))
    }

    @Test
    fun testCacheStatus() {
        val status = songSettingsCache.getCacheStatus()
        assertNotNull("缓存状态不应该为空", status)
        assertTrue("状态应该包含缓存信息", status.contains("缓存"))
    }

    @Test
    fun testTaskManagerStatus() {
        val status = backgroundTaskManager.getStatus()
        assertNotNull("任务管理器状态不应该为空", status)
        assertTrue("状态应该包含运行信息", status.contains("运行") || status.contains("线程"))
    }

    @Test
    fun testMemoryUsage() {
        val runtime = Runtime.getRuntime()
        val initialMemory = runtime.totalMemory() - runtime.freeMemory()
        
        // 创建大量缓存数据
        repeat(50) { index ->
            val uri = Uri.parse("content://media/external/audio/media/$index")
            songSettingsCache.setCustomName(uri, "测试歌曲$index")
            songSettingsCache.setSpeed(uri, 1.0f + index * 0.1f)
        }
        
        val afterCacheMemory = runtime.totalMemory() - runtime.freeMemory()
        val memoryIncrease = afterCacheMemory - initialMemory
        
        println("缓存50个歌曲设置后内存增加: ${memoryIncrease / 1024}KB")
        
        // 验证内存使用合理（应该小于1MB）
        assertTrue("内存使用应该合理", memoryIncrease < 1024 * 1024)
    }

    @Test
    fun testConcurrentAccess() = runBlocking {
        // 测试并发访问缓存
        val jobs = (1..10).map { index ->
            kotlinx.coroutines.launch {
                val uri = Uri.parse("content://media/external/audio/media/$index")
                songSettingsCache.setCustomName(uri, "并发测试$index")
                val settings = songSettingsCache.getSongSettings(uri)
                assertEquals("并发测试$index", settings.customName)
            }
        }
        
        jobs.forEach { it.join() }
        
        // 验证所有数据都正确保存
        repeat(10) { index ->
            val uri = Uri.parse("content://media/external/audio/media/${index + 1}")
            val settings = songSettingsCache.getSongSettings(uri)
            assertEquals("并发测试${index + 1}", settings.customName)
        }
    }
}
