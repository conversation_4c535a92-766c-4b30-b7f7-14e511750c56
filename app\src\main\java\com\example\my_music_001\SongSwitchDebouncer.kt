package com.example.my_music_001

import android.util.Log
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicLong

/**
 * 切歌防抖管理器
 * 防止快速连续点击导致的连跳多首歌曲问题
 */
class SongSwitchDebouncer {
    companion object {
        private const val TAG = "SongSwitchDebouncer"
        private const val DEBOUNCE_INTERVAL_MS = 500L // 防抖间隔500毫秒
        private const val FORCE_STOP_INTERVAL_MS = 100L // 强制停止间隔100毫秒
    }

    // 上次切歌时间
    private val lastSwitchTime = AtomicLong(0L)
    
    // 是否正在切歌
    private val isSwitching = AtomicBoolean(false)
    
    // 上次切歌方向（true=下一首，false=上一首）
    private var lastDirection: Boolean? = null
    
    // 连续同方向切歌计数
    private var consecutiveCount = 0
    
    // 上次强制停止时间
    private val lastForceStopTime = AtomicLong(0L)

    /**
     * 检查是否可以执行切歌操作
     * @param isNext true表示下一首，false表示上一首
     * @return true表示可以执行，false表示应该忽略
     */
    fun canSwitch(isNext: Boolean): Boolean {
        val currentTime = System.currentTimeMillis()
        val lastTime = lastSwitchTime.get()
        val timeDiff = currentTime - lastTime
        
        // 如果正在切歌中，直接拒绝
        if (isSwitching.get()) {
            Log.d(TAG, "正在切歌中，忽略新的切歌请求")
            return false
        }
        
        // 检查防抖间隔
        if (timeDiff < DEBOUNCE_INTERVAL_MS) {
            Log.d(TAG, "切歌间隔过短 (${timeDiff}ms < ${DEBOUNCE_INTERVAL_MS}ms)，忽略请求")
            return false
        }
        
        // 检查连续同方向切歌
        if (lastDirection == isNext) {
            consecutiveCount++
            if (consecutiveCount > 3 && timeDiff < DEBOUNCE_INTERVAL_MS * 2) {
                Log.d(TAG, "连续同方向切歌过多 ($consecutiveCount 次)，增加防抖时间")
                return false
            }
        } else {
            consecutiveCount = 1
            lastDirection = isNext
        }
        
        return true
    }

    /**
     * 开始切歌操作
     * @param isNext true表示下一首，false表示上一首
     * @return true表示成功开始，false表示被拒绝
     */
    fun startSwitch(isNext: Boolean): Boolean {
        if (!canSwitch(isNext)) {
            return false
        }
        
        val currentTime = System.currentTimeMillis()
        
        // 设置切歌状态
        if (isSwitching.compareAndSet(false, true)) {
            lastSwitchTime.set(currentTime)
            Log.d(TAG, "开始切歌操作: ${if (isNext) "下一首" else "上一首"}")
            return true
        }
        
        return false
    }

    /**
     * 完成切歌操作
     */
    fun finishSwitch() {
        if (isSwitching.compareAndSet(true, false)) {
            val currentTime = System.currentTimeMillis()
            val switchDuration = currentTime - lastSwitchTime.get()
            Log.d(TAG, "切歌操作完成，耗时: ${switchDuration}ms")
        }
    }

    /**
     * 强制停止当前播放（用于确保上一首歌曲停止）
     * @return true表示需要强制停止，false表示不需要
     */
    fun shouldForceStop(): Boolean {
        val currentTime = System.currentTimeMillis()
        val lastTime = lastForceStopTime.get()
        
        if (currentTime - lastTime > FORCE_STOP_INTERVAL_MS) {
            lastForceStopTime.set(currentTime)
            return true
        }
        
        return false
    }

    /**
     * 重置防抖状态（用于异常情况恢复）
     */
    fun reset() {
        isSwitching.set(false)
        lastSwitchTime.set(0L)
        lastDirection = null
        consecutiveCount = 0
        lastForceStopTime.set(0L)
        Log.d(TAG, "防抖状态已重置")
    }

    /**
     * 获取当前状态信息
     */
    fun getStatus(): String {
        val currentTime = System.currentTimeMillis()
        val lastTime = lastSwitchTime.get()
        val timeSinceLastSwitch = if (lastTime > 0) currentTime - lastTime else -1
        
        return "切歌状态: ${if (isSwitching.get()) "进行中" else "空闲"}, " +
                "距离上次切歌: ${if (timeSinceLastSwitch >= 0) "${timeSinceLastSwitch}ms" else "无"}, " +
                "连续切歌: $consecutiveCount 次, " +
                "方向: ${when (lastDirection) { true -> "下一首" false -> "上一首" null -> "无" }}"
    }

    /**
     * 检查是否处于切歌状态
     */
    fun isSwitchingNow(): Boolean = isSwitching.get()

    /**
     * 获取剩余防抖时间
     */
    fun getRemainingDebounceTime(): Long {
        val currentTime = System.currentTimeMillis()
        val lastTime = lastSwitchTime.get()
        val elapsed = currentTime - lastTime
        val remaining = DEBOUNCE_INTERVAL_MS - elapsed
        return if (remaining > 0) remaining else 0L
    }
}
